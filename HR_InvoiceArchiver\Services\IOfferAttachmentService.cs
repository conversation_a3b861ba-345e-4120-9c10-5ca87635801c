using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة مرفقات العروض
    /// </summary>
    public interface IOfferAttachmentService
    {
        /// <summary>
        /// رفع مرفق للعرض
        /// </summary>
        /// <param name="sourceFilePath">مسار الملف المصدر</param>
        /// <param name="offerId">معرف العرض</param>
        /// <returns>مسار المرفق النسبي</returns>
        Task<string?> UploadAttachmentAsync(string sourceFilePath, int offerId);

        /// <summary>
        /// رفع مرفق للعرض مع التخزين السحابي
        /// </summary>
        /// <param name="sourceFilePath">مسار الملف المصدر</param>
        /// <param name="offerId">معرف العرض</param>
        /// <param name="uploadToCloud">رفع إلى التخزين السحابي</param>
        /// <returns>مسار المرفق النسبي</returns>
        Task<string?> UploadAttachmentAsync(string sourceFilePath, int offerId, bool uploadToCloud);

        /// <summary>
        /// حذف مرفق العرض
        /// </summary>
        /// <param name="attachmentPath">مسار المرفق</param>
        /// <param name="deleteFromCloud">حذف من التخزين السحابي</param>
        /// <returns>نجح الحذف</returns>
        Task<bool> DeleteAttachmentAsync(string? attachmentPath, bool deleteFromCloud = false);

        /// <summary>
        /// فتح مرفق العرض
        /// </summary>
        /// <param name="attachmentPath">مسار المرفق النسبي</param>
        Task OpenAttachment(string? attachmentPath);

        /// <summary>
        /// التحقق من وجود المرفق
        /// </summary>
        /// <param name="attachmentPath">مسار المرفق النسبي</param>
        /// <returns>المرفق موجود</returns>
        bool AttachmentExists(string? attachmentPath);

        /// <summary>
        /// الحصول على معلومات المرفق
        /// </summary>
        /// <param name="attachmentPath">مسار المرفق النسبي</param>
        /// <returns>معلومات المرفق</returns>
        Task<AttachmentInfo?> GetAttachmentInfo(string? attachmentPath);

        /// <summary>
        /// التحقق من صحة الملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نتيجة التحقق</returns>
        FileValidationResult ValidateFile(string filePath);

        /// <summary>
        /// الحصول على أنواع الملفات المدعومة
        /// </summary>
        /// <returns>قائمة أنواع الملفات المدعومة</returns>
        string[] GetSupportedFileTypes();

        /// <summary>
        /// الحصول على فلتر الملفات للحوار
        /// </summary>
        /// <returns>فلتر الملفات</returns>
        string GetFileFilter();
    }

    /// <summary>
    /// معلومات المرفق
    /// </summary>
    public class AttachmentInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string Extension { get; set; } = string.Empty;
        public long SizeInBytes { get; set; }
        public string SizeFormatted { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public bool Exists { get; set; }
        public string FullPath { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة التحقق من صحة الملف
    /// </summary>
    public class FileValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public long FileSizeBytes { get; set; }
        public string FileExtension { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
    }
}
