<Window x:Class="HR_InvoiceArchiver.Windows.AdvancedReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="التقارير المتقدمة"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="اختر نوع التقرير المطلوب" 
                   Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- خيارات التقارير -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- تقرير أفضل العروض -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <RadioButton x:Name="BestOffersRadio"
                                     GroupName="ReportType"
                                     Content="تقرير أفضل العروض"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     IsChecked="True"
                                     Margin="0,0,0,10"/>
                        
                        <TextBlock Text="يعرض أفضل عرض لكل مادة علمية مع التحليل المتقدم والإحصائيات"
                                   FontSize="12"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- تقرير اتجاهات الأسعار -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <RadioButton x:Name="PriceTrendsRadio"
                                     GroupName="ReportType"
                                     Content="تقرير اتجاهات الأسعار"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     Margin="0,0,0,10"
                                     Checked="PriceTrendsRadio_Checked"/>
                        
                        <TextBlock Text="يحلل اتجاهات الأسعار خلال فترة زمنية محددة مع الرسوم البيانية"
                                   FontSize="12"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,10"/>

                        <!-- خيارات التاريخ -->
                        <Grid x:Name="DateRangeGrid" Visibility="Collapsed">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <DatePicker x:Name="FromDatePicker"
                                        Grid.Column="0"
                                        materialDesign:HintAssist.Hint="من تاريخ"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        FontSize="12"/>

                            <TextBlock Grid.Column="1" Text=" إلى " VerticalAlignment="Center" Margin="10,0"/>

                            <DatePicker x:Name="ToDatePicker"
                                        Grid.Column="2"
                                        materialDesign:HintAssist.Hint="إلى تاريخ"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        FontSize="12"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- تقرير مقارنة العروض -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <RadioButton x:Name="ComparisonRadio"
                                     GroupName="ReportType"
                                     Content="تقرير مقارنة العروض"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     Margin="0,0,0,10"
                                     Checked="ComparisonRadio_Checked"/>
                        
                        <TextBlock Text="يقارن جميع العروض لمادة علمية واحدة مع التحليل التفصيلي"
                                   FontSize="12"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,10"/>

                        <!-- اختيار المادة العلمية -->
                        <ComboBox x:Name="MaterialComboBox"
                                  materialDesign:HintAssist.Hint="اختر المادة العلمية"
                                  materialDesign:HintAssist.IsFloating="True"
                                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                  FontSize="12"
                                  Visibility="Collapsed"/>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button x:Name="GenerateButton"
                    Grid.Column="1"
                    Content="إنشاء التقرير"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Padding="20,8"
                    Margin="0,0,10,0"
                    Click="GenerateButton_Click"/>

            <Button x:Name="CancelButton"
                    Grid.Column="2"
                    Content="إلغاء"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Padding="20,8"
                    Click="CancelButton_Click"/>
        </Grid>
    </Grid>
</Window>
