using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Pages
{
    /// <summary>
    /// صفحة عروض المندوبين - نسخة مبسطة
    /// </summary>
    public partial class OffersPage : UserControl
    {
        private readonly IOfferService _offerService = null!;
        private readonly IOfferAttachmentService _attachmentService = null!;
        private readonly IOfferReportService _reportService = null!;
        private readonly IToastService _toastService = null!;
        private ObservableCollection<Offer> _offers = new();
        private List<Offer> _allOffers = new();
        private List<Offer> _originalOffers = new(); // للاحتفاظ بالبيانات الأصلية قبل الفلترة

        public OffersPage(IOfferService offerService, IToastService toastService)
        {
            try
            {
                InitializeComponent();

                // الحصول على الخدمات
                _offerService = offerService;
                _attachmentService = App.ServiceProvider.GetRequiredService<IOfferAttachmentService>();
                _reportService = App.ServiceProvider.GetRequiredService<IOfferReportService>();
                _toastService = toastService;

                // تهيئة المجموعات
                _offers = new ObservableCollection<Offer>();
                _allOffers = new List<Offer>();
                _originalOffers = new List<Offer>();

                // ربط البيانات
                OffersDataGrid.ItemsSource = _offers;

                // تحميل البيانات عند تحميل الصفحة
                Loaded += OffersPage_Loaded;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة OffersPage: {ex.Message}");

                // تهيئة افتراضية في حالة الخطأ
                _offers = new ObservableCollection<Offer>();
                _allOffers = new List<Offer>();
                _originalOffers = new List<Offer>();
            }
        }

        // Constructor بدون parameters للـ NavigationService
        public OffersPage() : this(
            App.ServiceProvider.GetRequiredService<IOfferService>(),
            App.ServiceProvider.GetRequiredService<IToastService>())
        {
        }

        private async void OffersPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadOffersAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في Page_Loaded: {ex.Message}");
                _toastService?.ShowError("خطأ في التحميل", "حدث خطأ أثناء تحميل الصفحة");
            }
        }

        private async Task LoadOffersAsync()
        {
            try
            {
                ShowLoading(true);

                var offers = await _offerService.GetActiveOffersAsync();
                _originalOffers = offers?.ToList() ?? new List<Offer>();
                _allOffers = new List<Offer>(_originalOffers);

                // تحميل قوائم الفلاتر
                LoadFilters();

                UpdateOffersDisplay();
                UpdateStatistics();

                // إظهار رسالة نجاح فقط إذا كان هناك عروض
                if (_allOffers.Any())
                {
                    _toastService?.ShowSuccess("تم تحميل العروض", $"تم تحميل {_allOffers.Count} عرض بنجاح");
                }
                else
                {
                    _toastService?.ShowInfo("لا توجد عروض", "لا توجد عروض متاحة حالياً");
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في تحميل العروض", ex.Message);
                _originalOffers = new List<Offer>();
                _allOffers = new List<Offer>();
                UpdateOffersDisplay();
                UpdateStatistics();
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void UpdateOffersDisplay()
        {
            try
            {
                _offers.Clear();
                if (_allOffers?.Any() == true)
                {
                    foreach (var offer in _allOffers)
                    {
                        _offers.Add(offer);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateOffersDisplay: {ex.Message}");
                _offers.Clear();
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                TotalOffersTextBlock.Text = $"إجمالي العروض: {_originalOffers?.Count ?? 0}";
                FilteredOffersTextBlock.Text = $"العروض المعروضة: {_offers?.Count ?? 0}";

                // حساب أفضل العروض بشكل محلي لتجنب الأخطاء
                var bestOffersCount = 0;
                if (_originalOffers?.Any() == true)
                {
                    bestOffersCount = _originalOffers
                        .GroupBy(o => o.ScientificName)
                        .Count();
                }
                BestOffersTextBlock.Text = $"أفضل العروض: {bestOffersCount}";

                // تحديث عدد النتائج
                UpdateResultsCount();
            }
            catch (Exception ex)
            {
                TotalOffersTextBlock.Text = "إجمالي العروض: 0";
                FilteredOffersTextBlock.Text = "العروض المعروضة: 0";
                BestOffersTextBlock.Text = "أفضل العروض: غير متاح";
                UpdateResultsCount();

                // تسجيل الخطأ بدلاً من إظهاره للمستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateStatistics: {ex.Message}");
            }
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingProgressBar.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
        }

        // أحداث الأزرار
        private void AddOfferButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new HR_InvoiceArchiver.Windows.AddEditOfferWindow();
                addWindow.Owner = Window.GetWindow(this);

                if (addWindow.ShowDialog() == true || addWindow.IsSaved)
                {
                    // إعادة تحميل البيانات
                    _ = LoadOffersAsync();
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", ex.Message);
            }
        }

        private async void BestOffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowLoading(true);

                // محاولة استخدام التحليل المتقدم أولاً
                try
                {
                    var bestOffersWithAnalysis = await _offerService.GetBestOffersWithAnalysisAsync();
                    if (bestOffersWithAnalysis.Any())
                    {
                        _allOffers = bestOffersWithAnalysis.Select(r => r.BestOffer).ToList();

                        // عرض معلومات إضافية عن التحليل
                        var totalSavings = bestOffersWithAnalysis.Sum(r => r.TotalSavings);
                        var avgQuality = bestOffersWithAnalysis.Average(r => r.BestOffer.Price); // مؤقت

                        _toastService?.ShowSuccess("أفضل العروض (تحليل متقدم)",
                            $"تم عرض {_allOffers.Count} من أفضل العروض\nإجمالي التوفير المقدر: {totalSavings:C}");
                    }
                    else
                    {
                        throw new Exception("لا توجد نتائج من التحليل المتقدم");
                    }
                }
                catch
                {
                    // استخدام الطريقة البسيطة كبديل
                    var bestOffers = await _offerService.GetBestOffersAsync();
                    _allOffers = bestOffers.ToList();

                    _toastService?.ShowSuccess("أفضل العروض", $"تم عرض {_allOffers.Count} من أفضل العروض");
                }

                UpdateOffersDisplay();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في تحميل أفضل العروض", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!_allOffers.Any())
                {
                    _toastService?.ShowWarning("تحذير", "لا توجد عروض للتصدير");
                    return;
                }

                ShowLoading(true);

                // اختيار مكان الحفظ
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "حفظ تقرير العروض",
                    Filter = "ملفات Excel|*.xlsx",
                    FileName = $"تقرير_العروض_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var excelData = await _offerService.ExportOffersToExcelAsync(_allOffers, "تقرير العروض");
                    await File.WriteAllBytesAsync(saveFileDialog.FileName, excelData);

                    _toastService?.ShowSuccess("تم التصدير", $"تم حفظ التقرير في: {saveFileDialog.FileName}");

                    // سؤال المستخدم إذا كان يريد فتح الملف
                    var result = MessageBox.Show(
                        "تم حفظ التقرير بنجاح. هل تريد فتحه الآن؟",
                        "تم التصدير",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = saveFileDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في التصدير", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadOffersAsync();
        }

        private void LoadFilters()
        {
            try
            {
                // تحميل قائمة المكاتب العلمية
                var offices = new List<string> { "جميع المكاتب" };

                if (_originalOffers?.Any() == true)
                {
                    var distinctOffices = _originalOffers
                        .Where(o => !string.IsNullOrEmpty(o.ScientificOffice))
                        .Select(o => o.ScientificOffice)
                        .Distinct()
                        .OrderBy(o => o)
                        .ToList();

                    offices.AddRange(distinctOffices);
                }

                OfficeFilterComboBox.ItemsSource = offices;
                OfficeFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ بدلاً من إظهاره
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadFilters: {ex.Message}");

                // تعيين قيم افتراضية
                OfficeFilterComboBox.ItemsSource = new List<string> { "جميع المكاتب" };
                OfficeFilterComboBox.SelectedIndex = 0;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void PriceFilter_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void OfficeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void DateFilter_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            // مسح جميع الفلاتر
            SearchTextBox.Text = "";
            MinPriceTextBox.Text = "";
            MaxPriceTextBox.Text = "";
            OfficeFilterComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;

            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                if (_originalOffers == null)
                {
                    _allOffers = new List<Offer>();
                    UpdateOffersDisplay();
                    UpdateStatistics();
                    return;
                }

                var filteredOffers = new List<Offer>(_originalOffers);

                // فلتر البحث النصي
                var searchTerm = SearchTextBox?.Text?.Trim()?.ToLower();
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredOffers = filteredOffers.Where(o =>
                        (o.ScientificName?.ToLower().Contains(searchTerm) ?? false) ||
                        (o.ScientificOffice?.ToLower().Contains(searchTerm) ?? false) ||
                        (o.RepresentativeName?.ToLower().Contains(searchTerm) ?? false) ||
                        (o.TradeName?.ToLower().Contains(searchTerm) ?? false) ||
                        (o.BonusOrDiscount?.ToLower().Contains(searchTerm) ?? false)
                    ).ToList();
                }

                // فلتر نطاق السعر
                if (decimal.TryParse(MinPriceTextBox?.Text, out decimal minPrice))
                {
                    filteredOffers = filteredOffers.Where(o => o.Price >= minPrice).ToList();
                }
                if (decimal.TryParse(MaxPriceTextBox?.Text, out decimal maxPrice))
                {
                    filteredOffers = filteredOffers.Where(o => o.Price <= maxPrice).ToList();
                }

                // فلتر المكتب العلمي
                var selectedOffice = OfficeFilterComboBox?.SelectedItem?.ToString();
                if (!string.IsNullOrEmpty(selectedOffice) && selectedOffice != "جميع المكاتب")
                {
                    filteredOffers = filteredOffers.Where(o => o.ScientificOffice == selectedOffice).ToList();
                }

                // فلتر نطاق التاريخ
                if (FromDatePicker?.SelectedDate.HasValue == true)
                {
                    filteredOffers = filteredOffers.Where(o => o.CreatedAt.Date >= FromDatePicker.SelectedDate.Value.Date).ToList();
                }
                if (ToDatePicker?.SelectedDate.HasValue == true)
                {
                    filteredOffers = filteredOffers.Where(o => o.CreatedAt.Date <= ToDatePicker.SelectedDate.Value.Date).ToList();
                }

                // فلتر البونص والخصم
                var bonusFilter = BonusFilterComboBox?.SelectedItem?.ToString();
                if (!string.IsNullOrEmpty(bonusFilter))
                {
                    switch (bonusFilter)
                    {
                        case "يوجد بونص/خصم":
                            filteredOffers = filteredOffers.Where(o => !string.IsNullOrEmpty(o.BonusOrDiscount)).ToList();
                            break;
                        case "لا يوجد بونص/خصم":
                            filteredOffers = filteredOffers.Where(o => string.IsNullOrEmpty(o.BonusOrDiscount)).ToList();
                            break;
                    }
                }

                // فلتر المرفقات
                var attachmentFilter = AttachmentFilterComboBox?.SelectedItem?.ToString();
                if (!string.IsNullOrEmpty(attachmentFilter))
                {
                    switch (attachmentFilter)
                    {
                        case "يوجد مرفق":
                            filteredOffers = filteredOffers.Where(o => !string.IsNullOrEmpty(o.AttachmentPath)).ToList();
                            break;
                        case "لا يوجد مرفق":
                            filteredOffers = filteredOffers.Where(o => string.IsNullOrEmpty(o.AttachmentPath)).ToList();
                            break;
                    }
                }

                // فلتر أفضل العروض فقط
                if (BestOffersOnlyCheckBox?.IsChecked == true)
                {
                    filteredOffers = GetBestOffersFromList(filteredOffers);
                }

                // ترتيب السعر
                var priceSort = PriceSortComboBox?.SelectedItem?.ToString();
                if (!string.IsNullOrEmpty(priceSort))
                {
                    switch (priceSort)
                    {
                        case "الأقل سعراً أولاً":
                            filteredOffers = filteredOffers.OrderBy(o => o.Price).ToList();
                            break;
                        case "الأعلى سعراً أولاً":
                            filteredOffers = filteredOffers.OrderByDescending(o => o.Price).ToList();
                            break;
                    }
                }

                _allOffers = filteredOffers;
                UpdateOffersDisplay();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ بدلاً من إظهاره للمستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في ApplyFilters: {ex.Message}");

                // تعيين قيم افتراضية
                _allOffers = _originalOffers?.ToList() ?? new List<Offer>();
                UpdateOffersDisplay();
                UpdateStatistics();
            }
        }

        private void OffersDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (OffersDataGrid.SelectedItem is Offer selectedOffer)
            {
                EditOffer(selectedOffer);
            }
        }

        private void EditOfferButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Offer offer)
            {
                EditOffer(offer);
            }
        }

        private async void DeleteOfferButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Offer offer)
            {
                await DeleteOffer(offer);
            }
        }

        private void OpenAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Offer offer)
            {
                OpenAttachment(offer);
            }
        }

        private void EditOffer(Offer offer)
        {
            try
            {
                var editWindow = new HR_InvoiceArchiver.Windows.AddEditOfferWindow(offer);
                editWindow.Owner = Window.GetWindow(this);

                if (editWindow.ShowDialog() == true || editWindow.IsSaved)
                {
                    // إعادة تحميل البيانات
                    _ = LoadOffersAsync();
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", ex.Message);
            }
        }

        private async Task DeleteOffer(Offer offer)
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العرض:\n{offer.ScientificName} - {offer.ScientificOffice}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    ShowLoading(true);
                    
                    var success = await _offerService.DeleteOfferAsync(offer.Id);
                    
                    if (success)
                    {
                        _toastService?.ShowSuccess("تم الحذف", "تم حذف العرض بنجاح");
                        await LoadOffersAsync();
                    }
                    else
                    {
                        _toastService?.ShowError("خطأ", "فشل في حذف العرض");
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في الحذف", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void OpenAttachment(Offer offer)
        {
            try
            {
                if (!offer.HasAttachment || string.IsNullOrEmpty(offer.AttachmentPath))
                {
                    _toastService?.ShowWarning("تحذير", "لا يوجد مرفق لهذا العرض");
                    return;
                }

                if (!File.Exists(offer.AttachmentPath))
                {
                    _toastService?.ShowError("خطأ", "الملف المرفق غير موجود");
                    return;
                }

                // فتح الملف بالتطبيق الافتراضي
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = offer.AttachmentPath,
                    UseShellExecute = true
                };

                Process.Start(processStartInfo);
                _toastService?.ShowSuccess("تم فتح المرفق", "تم فتح الملف المرفق");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في فتح المرفق", ex.Message);
            }
        }

        // دوال الفلاتر الجديدة
        private void BonusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void PriceSortFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void BestOffersFilter_Changed(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void AttachmentFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void AdvancedReportsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء نافذة اختيار نوع التقرير
                var reportWindow = new AdvancedReportsWindow();
                if (reportWindow.ShowDialog() == true)
                {
                    ShowLoading(true);

                    switch (reportWindow.SelectedReportType)
                    {
                        case "BestOffers":
                            await GenerateBestOffersReport();
                            break;
                        case "PriceTrends":
                            await GeneratePriceTrendsReport(reportWindow.FromDate, reportWindow.ToDate);
                            break;
                        case "Comparison":
                            await GenerateComparisonReport(reportWindow.SelectedMaterial);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في إنشاء التقرير", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async Task GenerateBestOffersReport()
        {
            try
            {
                var reportData = await _reportService.GenerateBestOffersReportAsync();
                await SaveAndOpenReport(reportData, "تقرير_أفضل_العروض.xlsx");
                _toastService?.ShowSuccess("تم إنشاء التقرير", "تم إنشاء تقرير أفضل العروض بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في إنشاء تقرير أفضل العروض", ex.Message);
            }
        }

        private async Task GeneratePriceTrendsReport(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var reportData = await _reportService.GeneratePriceTrendReportAsync(fromDate, toDate);
                await SaveAndOpenReport(reportData, $"تقرير_اتجاهات_الأسعار_{fromDate:yyyy-MM-dd}_إلى_{toDate:yyyy-MM-dd}.xlsx");
                _toastService?.ShowSuccess("تم إنشاء التقرير", "تم إنشاء تقرير اتجاهات الأسعار بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في إنشاء تقرير اتجاهات الأسعار", ex.Message);
            }
        }

        private async Task GenerateComparisonReport(string materialName)
        {
            try
            {
                var reportData = await _reportService.GenerateComparisonReportAsync(materialName);
                await SaveAndOpenReport(reportData, $"تقرير_مقارنة_{materialName}.xlsx");
                _toastService?.ShowSuccess("تم إنشاء التقرير", $"تم إنشاء تقرير مقارنة {materialName} بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في إنشاء تقرير المقارنة", ex.Message);
            }
        }

        private async Task SaveAndOpenReport(byte[] reportData, string fileName)
        {
            var saveFileDialog = new SaveFileDialog
            {
                FileName = fileName,
                Filter = "ملفات Excel|*.xlsx",
                Title = "حفظ التقرير"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                await File.WriteAllBytesAsync(saveFileDialog.FileName, reportData);

                // فتح الملف
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = saveFileDialog.FileName,
                    UseShellExecute = true
                });
            }
        }

        private List<Offer> GetBestOffersFromList(List<Offer> offers)
        {
            // تجميع العروض حسب المادة العلمية
            var groupedOffers = offers.GroupBy(o => o.ScientificName);
            var bestOffers = new List<Offer>();

            foreach (var group in groupedOffers)
            {
                // العثور على أفضل عرض لكل مادة علمية
                var bestOffer = group.OrderBy(o => o.Price).FirstOrDefault();
                if (bestOffer != null)
                {
                    bestOffers.Add(bestOffer);
                }
            }

            return bestOffers;
        }

        private void UpdateResultsCount()
        {
            var count = _allOffers?.Count ?? 0;
            if (ResultsCountTextBlock != null)
            {
                ResultsCountTextBlock.Text = count == 1 ? "نتيجة واحدة" : $"{count} نتيجة";
            }
        }
    }
}
