<UserControl x:Class="HR_InvoiceArchiver.Pages.OffersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignPaper}"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                       Text="عروض المندوبين" 
                       Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                       VerticalAlignment="Center"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="AddOfferButton"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        materialDesign:ButtonAssist.CornerRadius="5"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        Margin="10,0,0,0"
                        Click="AddOfferButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إضافة عرض جديد"/>
                    </StackPanel>
                </Button>

                <Button x:Name="BestOffersButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        materialDesign:ButtonAssist.CornerRadius="5"
                        Margin="10,0,0,0"
                        Click="BestOffersButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Star" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="أفضل العروض"/>
                    </StackPanel>
                </Button>

                <Button x:Name="ExportButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        materialDesign:ButtonAssist.CornerRadius="5"
                        Margin="10,0,0,0"
                        Click="ExportButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تصدير Excel"/>
                    </StackPanel>
                </Button>

                <Button x:Name="AdvancedReportsButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        materialDesign:ButtonAssist.CornerRadius="5"
                        Margin="10,0,0,0"
                        Click="AdvancedReportsButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تقارير متقدمة"/>
                    </StackPanel>
                </Button>

                <Button x:Name="RefreshButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        materialDesign:ButtonAssist.CornerRadius="5"
                        Margin="10,0,0,0"
                        Click="RefreshButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تحديث"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>

        <!-- شريط البحث والفلاتر -->
        <Expander Grid.Row="1"
                  Header="البحث والفلاتر"
                  IsExpanded="True"
                  Margin="0,0,0,20">
            <Grid Margin="15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث -->
                <TextBox x:Name="SearchTextBox"
                         Grid.Row="0"
                         materialDesign:HintAssist.Hint="البحث في العروض..."
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"
                         TextChanged="SearchTextBox_TextChanged">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- الفلاتر المتقدمة -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- فلتر نطاق السعر -->
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="نطاق السعر" FontSize="12" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="MinPriceTextBox"
                                     Grid.Column="0"
                                     materialDesign:HintAssist.Hint="من"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     FontSize="12"
                                     TextChanged="PriceFilter_TextChanged"/>

                            <TextBlock Grid.Column="1" Text=" - " VerticalAlignment="Center" Margin="5,0"/>

                            <TextBox x:Name="MaxPriceTextBox"
                                     Grid.Column="2"
                                     materialDesign:HintAssist.Hint="إلى"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     FontSize="12"
                                     TextChanged="PriceFilter_TextChanged"/>
                        </Grid>
                    </StackPanel>

                    <!-- فلتر المكتب العلمي -->
                    <ComboBox x:Name="OfficeFilterComboBox"
                              Grid.Column="1"
                              materialDesign:HintAssist.Hint="المكتب العلمي"
                              materialDesign:HintAssist.IsFloating="True"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              FontSize="12"
                              Margin="10,0"
                              SelectionChanged="OfficeFilter_SelectionChanged"/>

                    <!-- فلتر نطاق التاريخ -->
                    <StackPanel Grid.Column="2" Margin="10,0,10,0">
                        <TextBlock Text="نطاق التاريخ" FontSize="12" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <DatePicker x:Name="FromDatePicker"
                                        Grid.Column="0"
                                        materialDesign:HintAssist.Hint="من تاريخ"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        FontSize="12"
                                        SelectedDateChanged="DateFilter_SelectedDateChanged"/>

                            <TextBlock Grid.Column="1" Text=" - " VerticalAlignment="Center" Margin="5,0"/>

                            <DatePicker x:Name="ToDatePicker"
                                        Grid.Column="2"
                                        materialDesign:HintAssist.Hint="إلى تاريخ"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        FontSize="12"
                                        SelectedDateChanged="DateFilter_SelectedDateChanged"/>
                        </Grid>
                    </StackPanel>

                    <!-- فلتر البونص والخصم -->
                    <ComboBox x:Name="BonusFilterComboBox"
                              Grid.Column="3"
                              materialDesign:HintAssist.Hint="البونص/الخصم"
                              materialDesign:HintAssist.IsFloating="True"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              FontSize="12"
                              Margin="10,0"
                              SelectionChanged="BonusFilter_SelectionChanged">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="يوجد بونص/خصم"/>
                        <ComboBoxItem Content="لا يوجد بونص/خصم"/>
                    </ComboBox>

                    <!-- زر مسح الفلاتر -->
                    <Button x:Name="ClearFiltersButton"
                            Grid.Column="4"
                            Content="مسح الفلاتر"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="5"
                            Height="32"
                            Padding="15,0"
                            Margin="10,0,0,0"
                            Click="ClearFiltersButton_Click"/>
                </Grid>

                <!-- صف الفلاتر الإضافية -->
                <Grid Grid.Row="2" Margin="0,15,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- فلتر ترتيب السعر -->
                    <ComboBox x:Name="PriceSortComboBox"
                              Grid.Column="0"
                              materialDesign:HintAssist.Hint="ترتيب السعر"
                              materialDesign:HintAssist.IsFloating="True"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              FontSize="12"
                              Margin="0,0,10,0"
                              SelectionChanged="PriceSortFilter_SelectionChanged">
                        <ComboBoxItem Content="بدون ترتيب"/>
                        <ComboBoxItem Content="الأقل سعراً أولاً"/>
                        <ComboBoxItem Content="الأعلى سعراً أولاً"/>
                    </ComboBox>

                    <!-- فلتر أفضل العروض -->
                    <CheckBox x:Name="BestOffersOnlyCheckBox"
                              Grid.Column="1"
                              Content="أفضل العروض فقط"
                              FontSize="12"
                              Margin="10,0"
                              Checked="BestOffersFilter_Changed"
                              Unchecked="BestOffersFilter_Changed"/>

                    <!-- فلتر وجود مرفقات -->
                    <ComboBox x:Name="AttachmentFilterComboBox"
                              Grid.Column="2"
                              materialDesign:HintAssist.Hint="المرفقات"
                              materialDesign:HintAssist.IsFloating="True"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              FontSize="12"
                              Margin="10,0"
                              SelectionChanged="AttachmentFilter_SelectionChanged">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="يوجد مرفق"/>
                        <ComboBoxItem Content="لا يوجد مرفق"/>
                    </ComboBox>

                    <!-- عدد النتائج -->
                    <TextBlock x:Name="ResultsCountTextBlock"
                               Grid.Column="3"
                               Text="0 نتيجة"
                               FontSize="12"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </Grid>
            </Grid>
        </Expander>

        <!-- جدول العروض -->
        <Border Grid.Row="2"
                Background="White"
                CornerRadius="8">
            <DataGrid x:Name="OffersDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      materialDesign:DataGridAssist.CellPadding="8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                      FontSize="13"
                      MouseDoubleClick="OffersDataGrid_MouseDoubleClick">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="المكتب العلمي" 
                                        Binding="{Binding ScientificOffice}" 
                                        Width="150"/>
                    
                    <DataGridTextColumn Header="اسم المندوب" 
                                        Binding="{Binding RepresentativeName}" 
                                        Width="120"/>
                    
                    <DataGridTextColumn Header="المادة العلمية" 
                                        Binding="{Binding ScientificName}" 
                                        Width="150"/>
                    
                    <DataGridTextColumn Header="المادة التجارية" 
                                        Binding="{Binding TradeName}" 
                                        Width="120"/>
                    
                    <DataGridTextColumn Header="السعر" 
                                        Binding="{Binding Price, StringFormat='{}{0:C}'}" 
                                        Width="100"/>
                    
                    <DataGridTextColumn Header="البونص/الخصم" 
                                        Binding="{Binding BonusDiscountDisplay}" 
                                        Width="120"/>
                    
                    <DataGridTextColumn Header="تاريخ الإضافة"
                                        Binding="{Binding CreatedAt, StringFormat='{}{0:yyyy/MM/dd}'}"
                                        Width="100"/>

                    <DataGridTextColumn Header="المرفق"
                                        Binding="{Binding AttachmentInfo}"
                                        Width="120"/>

                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="فتح المرفق"
                                            Click="OpenAttachmentButton_Click"
                                            Tag="{Binding}"
                                            Visibility="{Binding HasAttachment, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <materialDesign:PackIcon Kind="Attachment" Width="16" Height="16"/>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="تعديل"
                                            Click="EditOfferButton_Click"
                                            Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="حذف"
                                            Click="DeleteOfferButton_Click"
                                            Tag="{Binding}"
                                            Foreground="Red">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- شريط الحالة -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <Border Background="{DynamicResource PrimaryHueLightBrush}"
                        CornerRadius="15"
                        Padding="10,5"
                        Margin="0,0,10,0">
                    <TextBlock x:Name="TotalOffersTextBlock"
                               Text="إجمالي العروض: 0"
                               Foreground="White"
                               FontWeight="Medium"
                               FontSize="12"/>
                </Border>

                <Border Background="{DynamicResource SecondaryHueMidBrush}"
                        CornerRadius="15"
                        Padding="10,5"
                        Margin="0,0,10,0">
                    <TextBlock x:Name="FilteredOffersTextBlock"
                               Text="العروض المعروضة: 0"
                               Foreground="White"
                               FontWeight="Medium"
                               FontSize="12"/>
                </Border>

                <Border Background="{DynamicResource PrimaryHueDarkBrush}"
                        CornerRadius="15"
                        Padding="10,5">
                    <TextBlock x:Name="BestOffersTextBlock"
                               Text="أفضل العروض: 0"
                               Foreground="White"
                               FontWeight="Medium"
                               FontSize="12"/>
                </Border>
            </StackPanel>

            <ProgressBar x:Name="LoadingProgressBar"
                         Grid.Column="1"
                         Style="{StaticResource MaterialDesignCircularProgressBar}"
                         Width="20"
                         Height="20"
                         IsIndeterminate="True"
                         Visibility="Collapsed"/>
        </Grid>
    </Grid>
</UserControl>
