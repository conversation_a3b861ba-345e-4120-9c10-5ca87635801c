<UserControl x:Class="HR_InvoiceArchiver.Pages.OffersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
             Background="#F8F9FF"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Converters -->
        <converters:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Search Box Style -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                                <TextBlock x:Name="PlaceholderText"
                                         Text="🔍 البحث في العروض..."
                                         Foreground="#9E9E9E"
                                         VerticalAlignment="Center"
                                         Margin="{TemplateBinding Padding}"
                                         IsHitTestVisible="False"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="Text" Value="{x:Null}">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="Text" Value=""/>
                                    <Condition Property="IsFocused" Value="False"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان مع أيقونة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FileDocument"
                                           Width="32" Height="32"
                                           Foreground="{StaticResource PrimaryGradientBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="عروض المندوبين"
                                   FontSize="24" FontWeight="Bold"
                                   Foreground="#2D3748"/>
                        <TextBlock Text="إدارة ومتابعة عروض الأسعار من المندوبين"
                                   FontSize="14"
                                   Foreground="#718096"
                                   Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddOfferButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="{StaticResource PrimaryGradientBrush}"
                            Margin="8,0,0,0"
                            Click="AddOfferButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة عرض جديد" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="BestOffersButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="{StaticResource SecondaryGradientBrush}"
                            Margin="8,0,0,0"
                            Click="BestOffersButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Star" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="أفضل العروض" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ExportButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource PrimaryGradientBrush}"
                            Foreground="{StaticResource PrimaryGradientBrush}"
                            Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير Excel" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="AdvancedReportsButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource SecondaryGradientBrush}"
                            Foreground="{StaticResource SecondaryGradientBrush}"
                            Click="AdvancedReportsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تقارير متقدمة" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلاتر -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Expander Header="🔍 البحث والفلاتر المتقدمة"
                      IsExpanded="True"
                      FontSize="16"
                      FontWeight="Medium"
                      Foreground="#2D3748">
                <Grid Margin="20,15,20,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط البحث -->
                    <TextBox x:Name="SearchTextBox"
                             Grid.Row="0"
                             Style="{StaticResource SearchBoxStyle}"
                             Margin="0,0,0,20"
                             TextChanged="SearchTextBox_TextChanged">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- الفلاتر المتقدمة -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- الصف الأول من الفلاتر -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- فلتر نطاق السعر -->
                            <Border Grid.Column="0"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12"
                                    Margin="0,0,8,0">
                                <StackPanel>
                                    <TextBlock Text="💰 نطاق السعر"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBox x:Name="MinPriceTextBox"
                                                 Grid.Column="0"
                                                 materialDesign:HintAssist.Hint="من"
                                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                 FontSize="12"
                                                 Height="35"
                                                 TextChanged="PriceFilter_TextChanged"/>

                                        <TextBlock Grid.Column="1" Text=" - "
                                                   VerticalAlignment="Center"
                                                   Margin="8,0"
                                                   Foreground="#718096"/>

                                        <TextBox x:Name="MaxPriceTextBox"
                                                 Grid.Column="2"
                                                 materialDesign:HintAssist.Hint="إلى"
                                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                 FontSize="12"
                                                 Height="35"
                                                 TextChanged="PriceFilter_TextChanged"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- فلتر المكتب العلمي -->
                            <Border Grid.Column="1"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12"
                                    Margin="0,0,8,0">
                                <StackPanel>
                                    <TextBlock Text="🏢 المكتب العلمي"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <ComboBox x:Name="OfficeFilterComboBox"
                                              materialDesign:HintAssist.Hint="اختر المكتب"
                                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                              FontSize="12"
                                              Height="35"
                                              SelectionChanged="OfficeFilter_SelectionChanged"/>
                                </StackPanel>
                            </Border>

                            <!-- فلتر نطاق التاريخ -->
                            <Border Grid.Column="2"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12"
                                    Margin="0,0,8,0">
                                <StackPanel>
                                    <TextBlock Text="📅 نطاق التاريخ"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <DatePicker x:Name="FromDatePicker"
                                                    Grid.Column="0"
                                                    materialDesign:HintAssist.Hint="من تاريخ"
                                                    Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                                    FontSize="12"
                                                    Height="35"
                                                    SelectedDateChanged="DateFilter_SelectedDateChanged"/>

                                        <TextBlock Grid.Column="1" Text=" - "
                                                   VerticalAlignment="Center"
                                                   Margin="8,0"
                                                   Foreground="#718096"/>

                                        <DatePicker x:Name="ToDatePicker"
                                                    Grid.Column="2"
                                                    materialDesign:HintAssist.Hint="إلى تاريخ"
                                                    Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                                    FontSize="12"
                                                    Height="35"
                                                    SelectedDateChanged="DateFilter_SelectedDateChanged"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                            </Border>

                            <!-- فلتر البونص والخصم -->
                            <Border Grid.Column="3"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12">
                                <StackPanel>
                                    <TextBlock Text="🎁 البونص والخصم"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <ComboBox x:Name="BonusFilterComboBox"
                                              materialDesign:HintAssist.Hint="اختر النوع"
                                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                              FontSize="12"
                                              Height="35"
                                              SelectionChanged="BonusFilter_SelectionChanged">
                                        <ComboBoxItem Content="الكل"/>
                                        <ComboBoxItem Content="يوجد بونص/خصم"/>
                                        <ComboBoxItem Content="لا يوجد بونص/خصم"/>
                                    </ComboBox>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- الصف الثاني من الفلاتر -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- فلتر ترتيب السعر -->
                            <Border Grid.Column="0"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12"
                                    Margin="0,0,8,0">
                                <StackPanel>
                                    <TextBlock Text="📊 ترتيب السعر"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <ComboBox x:Name="PriceSortComboBox"
                                              materialDesign:HintAssist.Hint="اختر الترتيب"
                                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                              FontSize="12"
                                              Height="35"
                                              SelectionChanged="PriceSortFilter_SelectionChanged">
                                        <ComboBoxItem Content="بدون ترتيب"/>
                                        <ComboBoxItem Content="الأقل سعراً أولاً"/>
                                        <ComboBoxItem Content="الأعلى سعراً أولاً"/>
                                    </ComboBox>
                                </StackPanel>
                            </Border>

                            <!-- فلتر وجود مرفقات -->
                            <Border Grid.Column="1"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12"
                                    Margin="0,0,8,0">
                                <StackPanel>
                                    <TextBlock Text="📎 المرفقات"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <ComboBox x:Name="AttachmentFilterComboBox"
                                              materialDesign:HintAssist.Hint="حالة المرفقات"
                                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                              FontSize="12"
                                              Height="35"
                                              SelectionChanged="AttachmentFilter_SelectionChanged">
                                        <ComboBoxItem Content="الكل"/>
                                        <ComboBoxItem Content="يوجد مرفقات"/>
                                        <ComboBoxItem Content="لا يوجد مرفقات"/>
                                    </ComboBox>
                                </StackPanel>
                            </Border>

                            <!-- فلتر أفضل العروض -->
                            <Border Grid.Column="2"
                                    Background="#F7FAFC"
                                    CornerRadius="8"
                                    Padding="12"
                                    Margin="0,0,8,0">
                                <StackPanel>
                                    <TextBlock Text="⭐ خيارات متقدمة"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,8"/>
                                    <CheckBox x:Name="BestOffersOnlyCheckBox"
                                              Content="أفضل العروض فقط"
                                              FontSize="12"
                                              FontWeight="Medium"
                                              Foreground="#2D3748"
                                              Checked="BestOffersFilter_Changed"
                                              Unchecked="BestOffersFilter_Changed"/>
                                </StackPanel>
                            </Border>

                            <!-- زر مسح الفلاتر وعدد النتائج -->
                            <StackPanel Grid.Column="3" VerticalAlignment="Center">
                                <Button x:Name="ClearFiltersButton"
                                        Content="🗑️ مسح الفلاتر"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        materialDesign:ButtonAssist.CornerRadius="8"
                                        Height="40"
                                        Padding="16,8"
                                        FontWeight="Medium"
                                        BorderBrush="#E53E3E"
                                        Foreground="#E53E3E"
                                        Margin="0,0,0,8"
                                        Click="ClearFiltersButton_Click"/>

                                <!-- عدد النتائج -->
                                <TextBlock x:Name="ResultsCountTextBlock"
                                           Text="0 نتيجة"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           HorizontalAlignment="Center"
                                           Foreground="#718096"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Grid>
            </Expander>
        </materialDesign:Card>
        <!-- جدول العروض -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Border Grid.Row="0"
                        Background="{StaticResource PrimaryGradientBrush}"
                        CornerRadius="8,8,0,0"
                        Padding="20,15">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TableLarge"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="قائمة العروض"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TotalCountTextBlock"
                                   Text="(0 عرض)"
                                   FontSize="14"
                                   Foreground="White"
                                   Opacity="0.8"
                                   VerticalAlignment="Center"
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- الجدول -->
                <DataGrid x:Name="OffersDataGrid"
                          Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          materialDesign:DataGridAssist.CellPadding="12"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="12"
                          FontSize="13"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#F8F9FA"
                          MouseDoubleClick="OffersDataGrid_MouseDoubleClick">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#2D3748"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="13"/>
                            <Setter Property="Height" Value="45"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                            <Setter Property="Height" Value="50"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E6F3FF"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#CCE7FF"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="🏢 المكتب العلمي"
                                            Binding="{Binding ScientificOffice}"
                                            Width="160"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTextColumn Header="👤 اسم المندوب"
                                            Binding="{Binding RepresentativeName}"
                                            Width="130"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTextColumn Header="🧪 المادة العلمية"
                                            Binding="{Binding ScientificName}"
                                            Width="160"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTextColumn Header="🏷️ المادة التجارية"
                                            Binding="{Binding TradeName}"
                                            Width="130"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTextColumn Header="💰 السعر"
                                            Binding="{Binding Price, StringFormat='{}{0:N0} ج.م'}"
                                            Width="110">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Foreground" Value="#059669"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="🎁 البونص/الخصم"
                                            Binding="{Binding BonusDiscountDisplay}"
                                            Width="130"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTextColumn Header="📅 تاريخ الإضافة"
                                            Binding="{Binding CreatedAt, StringFormat='{}{0:yyyy/MM/dd}'}"
                                            Width="120"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTextColumn Header="📎 المرفق"
                                            Binding="{Binding AttachmentInfo}"
                                            Width="100"
                                            ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>

                        <DataGridTemplateColumn Header="⚙️ الإجراءات" Width="160">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="فتح المرفق"
                                                Click="OpenAttachmentButton_Click"
                                                Tag="{Binding}"
                                                Background="#E6F3FF"
                                                Foreground="#1976D2"
                                                Width="32" Height="32"
                                                Margin="2"
                                                Visibility="{Binding HasAttachment, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <materialDesign:PackIcon Kind="Attachment" Width="18" Height="18"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="تعديل العرض"
                                                Click="EditOfferButton_Click"
                                                Tag="{Binding}"
                                                Background="#FFF3E0"
                                                Foreground="#F57C00"
                                                Width="32" Height="32"
                                                Margin="2">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="حذف العرض"
                                                Click="DeleteOfferButton_Click"
                                                Tag="{Binding}"
                                                Background="#FFEBEE"
                                                Foreground="#D32F2F"
                                                Width="32" Height="32"
                                                Margin="2">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- شريط الحالة -->
        <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="0,20,0,0">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- إحصائيات العروض -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="{StaticResource PrimaryGradientBrush}"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocument"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="TotalOffersTextBlock"
                                       Text="إجمالي العروض: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{StaticResource SecondaryGradientBrush}"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Filter"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="FilteredOffersTextBlock"
                                       Text="العروض المعروضة: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#FF6B6B"
                            CornerRadius="20"
                            Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Star"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="BestOffersTextBlock"
                                       Text="أفضل العروض: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
                <!-- مؤشر التحميل -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <ProgressBar x:Name="LoadingProgressBar"
                                 Style="{StaticResource MaterialDesignCircularProgressBar}"
                                 Width="24"
                                 Height="24"
                                 IsIndeterminate="True"
                                 Visibility="Collapsed"
                                 Margin="0,0,10,0"/>

                    <TextBlock Text="آخر تحديث: الآن"
                               FontSize="12"
                               Foreground="#718096"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
