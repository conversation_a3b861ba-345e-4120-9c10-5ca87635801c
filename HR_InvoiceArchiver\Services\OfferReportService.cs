using HR_InvoiceArchiver.Models;
using OfficeOpenXml;
using OfficeOpenXml.Drawing.Chart;
using OfficeOpenXml.Style;
using System.Drawing;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة تقارير العروض المتقدمة
    /// </summary>
    public class OfferReportService : IOfferReportService
    {
        private readonly IOfferService _offerService;
        private readonly IAdvancedOfferAnalysisService _analysisService;
        private readonly ILoggingService? _loggingService;

        public OfferReportService(
            IOfferService offerService,
            IAdvancedOfferAnalysisService analysisService,
            ILoggingService? loggingService = null)
        {
            _offerService = offerService;
            _analysisService = analysisService;
            _loggingService = loggingService;
        }

        public async Task<byte[]> GenerateComparisonReportAsync(string scientificName)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var comparisons = await _offerService.CompareOffersForMaterialAsync(scientificName);
                
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add($"مقارنة عروض {scientificName}");

                // إعداد الرؤوس
                var headers = new[]
                {
                    "الترتيب", "المكتب العلمي", "اسم المندوب", "السعر الأصلي", "السعر الفعلي",
                    "قيمة التوفير", "نقاط الجودة", "البونص/الخصم", "المزايا التنافسية", "التوصيات"
                };

                // كتابة الرؤوس
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                }

                // كتابة البيانات
                int row = 2;
                foreach (var comparison in comparisons)
                {
                    worksheet.Cells[row, 1].Value = comparison.Rank;
                    worksheet.Cells[row, 2].Value = comparison.Offer.ScientificOffice;
                    worksheet.Cells[row, 3].Value = comparison.Offer.RepresentativeName;
                    worksheet.Cells[row, 4].Value = comparison.Analysis.OriginalPrice;
                    worksheet.Cells[row, 5].Value = comparison.Analysis.EffectivePrice;
                    worksheet.Cells[row, 6].Value = comparison.Analysis.TotalSavings;
                    worksheet.Cells[row, 7].Value = comparison.Analysis.QualityScore;
                    worksheet.Cells[row, 8].Value = comparison.Offer.BonusOrDiscount ?? "لا يوجد";
                    worksheet.Cells[row, 9].Value = string.Join(", ", comparison.CompetitiveAdvantages);
                    worksheet.Cells[row, 10].Value = string.Join(", ", comparison.Recommendations);

                    // تلوين الصف الأول (أفضل عرض)
                    if (comparison.Rank == 1)
                    {
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                        }
                    }

                    row++;
                }

                // تنسيق الأعمدة
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                // إضافة رسم بياني
                if (comparisons.Count > 1)
                {
                    var chart = worksheet.Drawings.AddChart("PriceComparison", eChartType.ColumnClustered);
                    chart.Title.Text = $"مقارنة أسعار {scientificName}";
                    chart.SetPosition(row + 2, 0, 1, 0);
                    chart.SetSize(600, 300);

                    var series = chart.Series.Add(
                        worksheet.Cells[2, 5, row - 1, 5], // السعر الفعلي
                        worksheet.Cells[2, 2, row - 1, 2]  // أسماء المكاتب
                    );
                    series.Header = "السعر الفعلي";
                }

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في إنشاء تقرير المقارنة: {ex.Message}", ex);
                throw;
            }
        }

        public async Task<byte[]> GeneratePriceTrendReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var allOffers = await _offerService.GetAllOffersAsync();
                var filteredOffers = allOffers.Where(o => o.CreatedAt >= fromDate && o.CreatedAt <= toDate).ToList();

                using var package = new ExcelPackage();
                
                // ورقة البيانات الأساسية
                var dataSheet = package.Workbook.Worksheets.Add("بيانات الاتجاهات");
                await CreatePriceTrendDataSheet(dataSheet, filteredOffers);

                // ورقة التحليل
                var analysisSheet = package.Workbook.Worksheets.Add("تحليل الاتجاهات");
                await CreatePriceTrendAnalysisSheet(analysisSheet, filteredOffers);

                // ورقة الرسوم البيانية
                var chartsSheet = package.Workbook.Worksheets.Add("الرسوم البيانية");
                await CreatePriceTrendChartsSheet(chartsSheet, filteredOffers);

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في إنشاء تقرير اتجاهات الأسعار: {ex.Message}", ex);
                throw;
            }
        }

        public async Task<byte[]> GenerateBestOffersReportAsync()
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var bestOffers = await _offerService.GetBestOffersWithAnalysisAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("أفضل العروض");

                // إعداد الرؤوس
                var headers = new[]
                {
                    "المادة العلمية", "المكتب العلمي", "اسم المندوب", "السعر الأصلي", "السعر الفعلي",
                    "قيمة التوفير", "عدد العروض المنافسة", "أقل سعر", "أعلى سعر", "متوسط السعر", "تفاصيل التحليل"
                };

                // كتابة الرؤوس
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.Gold);
                }

                // كتابة البيانات
                int row = 2;
                foreach (var bestOffer in bestOffers)
                {
                    worksheet.Cells[row, 1].Value = bestOffer.ScientificName;
                    worksheet.Cells[row, 2].Value = bestOffer.BestOffer.ScientificOffice;
                    worksheet.Cells[row, 3].Value = bestOffer.BestOffer.RepresentativeName;
                    worksheet.Cells[row, 4].Value = bestOffer.BestOffer.Price;
                    worksheet.Cells[row, 5].Value = bestOffer.EffectivePrice;
                    worksheet.Cells[row, 6].Value = bestOffer.TotalSavings;
                    worksheet.Cells[row, 7].Value = bestOffer.CompetingOffersCount;
                    worksheet.Cells[row, 8].Value = bestOffer.PriceRange.MinPrice;
                    worksheet.Cells[row, 9].Value = bestOffer.PriceRange.MaxPrice;
                    worksheet.Cells[row, 10].Value = bestOffer.PriceRange.AveragePrice;
                    worksheet.Cells[row, 11].Value = bestOffer.AnalysisDetails;

                    row++;
                }

                // تنسيق الأعمدة
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                // إضافة ملخص إحصائي
                await AddStatisticalSummary(worksheet, bestOffers, row + 2);

                // إضافة رسوم بيانية
                if (bestOffers.Count > 1)
                {
                    await AddBestOffersCharts(worksheet, bestOffers, row + 10);
                }

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في إنشاء تقرير أفضل العروض: {ex.Message}", ex);
                throw;
            }
        }

        public async Task<OfferTrendAnalysis> AnalyzePriceTrendsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var allOffers = await _offerService.GetAllOffersAsync();
                var filteredOffers = allOffers.Where(o => o.CreatedAt >= fromDate && o.CreatedAt <= toDate).ToList();

                var analysis = new OfferTrendAnalysis
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalOffers = filteredOffers.Count
                };

                if (!filteredOffers.Any())
                {
                    return analysis;
                }

                // تحليل الاتجاهات حسب المادة العلمية
                var materialTrends = filteredOffers
                    .GroupBy(o => o.ScientificName)
                    .Select(g => AnalyzeMaterialTrend(g.Key, g.ToList()))
                    .ToList();

                analysis.MaterialTrends = materialTrends;

                // تحليل الاتجاهات الإجمالية
                analysis.OverallTrend = CalculateOverallTrend(filteredOffers);
                analysis.AveragePrice = filteredOffers.Average(o => o.Price);
                analysis.MedianPrice = CalculateMedian(filteredOffers.Select(o => o.Price).ToList());
                analysis.PriceVolatility = CalculatePriceVolatility(filteredOffers);

                // تحليل المكاتب العلمية
                analysis.TopPerformingOffices = GetTopPerformingOffices(filteredOffers);
                analysis.MostActiveOffices = GetMostActiveOffices(filteredOffers);

                return analysis;
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في تحليل اتجاهات الأسعار: {ex.Message}", ex);
                return new OfferTrendAnalysis { FromDate = fromDate, ToDate = toDate };
            }
        }

        private async Task CreatePriceTrendDataSheet(ExcelWorksheet worksheet, List<Offer> offers)
        {
            // تنفيذ إنشاء ورقة بيانات الاتجاهات
            var headers = new[] { "التاريخ", "المادة العلمية", "المكتب العلمي", "السعر", "البونص/الخصم" };
            
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            int row = 2;
            foreach (var offer in offers.OrderBy(o => o.CreatedAt))
            {
                worksheet.Cells[row, 1].Value = offer.CreatedAt.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 2].Value = offer.ScientificName;
                worksheet.Cells[row, 3].Value = offer.ScientificOffice;
                worksheet.Cells[row, 4].Value = offer.Price;
                worksheet.Cells[row, 5].Value = offer.BonusOrDiscount ?? "";
                row++;
            }

            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreatePriceTrendAnalysisSheet(ExcelWorksheet worksheet, List<Offer> offers)
        {
            // تحليل الاتجاهات حسب المادة العلمية
            var materialAnalysis = offers
                .GroupBy(o => o.ScientificName)
                .Select(g => new
                {
                    Material = g.Key,
                    Count = g.Count(),
                    AvgPrice = g.Average(o => o.Price),
                    MinPrice = g.Min(o => o.Price),
                    MaxPrice = g.Max(o => o.Price),
                    PriceRange = g.Max(o => o.Price) - g.Min(o => o.Price)
                })
                .OrderByDescending(a => a.Count)
                .ToList();

            var headers = new[] { "المادة العلمية", "عدد العروض", "متوسط السعر", "أقل سعر", "أعلى سعر", "نطاق السعر" };
            
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            int row = 2;
            foreach (var analysis in materialAnalysis)
            {
                worksheet.Cells[row, 1].Value = analysis.Material;
                worksheet.Cells[row, 2].Value = analysis.Count;
                worksheet.Cells[row, 3].Value = analysis.AvgPrice;
                worksheet.Cells[row, 4].Value = analysis.MinPrice;
                worksheet.Cells[row, 5].Value = analysis.MaxPrice;
                worksheet.Cells[row, 6].Value = analysis.PriceRange;
                row++;
            }

            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreatePriceTrendChartsSheet(ExcelWorksheet worksheet, List<Offer> offers)
        {
            // إنشاء رسوم بيانية للاتجاهات
            worksheet.Cells[1, 1].Value = "الرسوم البيانية لاتجاهات الأسعار";
            worksheet.Cells[1, 1].Style.Font.Bold = true;
            worksheet.Cells[1, 1].Style.Font.Size = 16;
        }

        private async Task AddStatisticalSummary(ExcelWorksheet worksheet, List<BestOfferResult> bestOffers, int startRow)
        {
            worksheet.Cells[startRow, 1].Value = "الملخص الإحصائي";
            worksheet.Cells[startRow, 1].Style.Font.Bold = true;
            worksheet.Cells[startRow, 1].Style.Font.Size = 14;

            worksheet.Cells[startRow + 2, 1].Value = "إجمالي المواد العلمية:";
            worksheet.Cells[startRow + 2, 2].Value = bestOffers.Count;

            worksheet.Cells[startRow + 3, 1].Value = "متوسط التوفير:";
            worksheet.Cells[startRow + 3, 2].Value = bestOffers.Average(b => b.TotalSavings);

            worksheet.Cells[startRow + 4, 1].Value = "إجمالي التوفير:";
            worksheet.Cells[startRow + 4, 2].Value = bestOffers.Sum(b => b.TotalSavings);
        }

        private async Task AddBestOffersCharts(ExcelWorksheet worksheet, List<BestOfferResult> bestOffers, int startRow)
        {
            // إضافة رسوم بيانية لأفضل العروض
        }

        private MaterialTrend AnalyzeMaterialTrend(string materialName, List<Offer> offers)
        {
            return new MaterialTrend
            {
                MaterialName = materialName,
                OfferCount = offers.Count,
                AveragePrice = offers.Average(o => o.Price),
                MinPrice = offers.Min(o => o.Price),
                MaxPrice = offers.Max(o => o.Price),
                PriceVolatility = CalculatePriceVolatility(offers),
                Trend = CalculateTrendDirection(offers)
            };
        }

        private TrendDirection CalculateOverallTrend(List<Offer> offers)
        {
            if (offers.Count < 2) return TrendDirection.Stable;

            var orderedOffers = offers.OrderBy(o => o.CreatedAt).ToList();
            var firstHalf = orderedOffers.Take(orderedOffers.Count / 2).Average(o => o.Price);
            var secondHalf = orderedOffers.Skip(orderedOffers.Count / 2).Average(o => o.Price);

            var change = (secondHalf - firstHalf) / firstHalf;

            if (change > 0.05m) return TrendDirection.Increasing;
            if (change < -0.05m) return TrendDirection.Decreasing;
            return TrendDirection.Stable;
        }

        private decimal CalculateMedian(List<decimal> values)
        {
            var sorted = values.OrderBy(v => v).ToList();
            int count = sorted.Count;
            
            if (count % 2 == 0)
            {
                return (sorted[count / 2 - 1] + sorted[count / 2]) / 2;
            }
            else
            {
                return sorted[count / 2];
            }
        }

        private decimal CalculatePriceVolatility(List<Offer> offers)
        {
            if (offers.Count < 2) return 0;

            var prices = offers.Select(o => o.Price).ToList();
            var mean = prices.Average();
            var variance = prices.Sum(p => (p - mean) * (p - mean)) / prices.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        private TrendDirection CalculateTrendDirection(List<Offer> offers)
        {
            return CalculateOverallTrend(offers);
        }

        private List<OfficePerformance> GetTopPerformingOffices(List<Offer> offers)
        {
            return offers
                .GroupBy(o => o.ScientificOffice)
                .Select(g => new OfficePerformance
                {
                    OfficeName = g.Key,
                    OfferCount = g.Count(),
                    AveragePrice = g.Average(o => o.Price),
                    BestPriceRatio = g.Count(o => o.Price == g.Min(x => x.Price)) / (decimal)g.Count()
                })
                .OrderByDescending(p => p.BestPriceRatio)
                .Take(10)
                .ToList();
        }

        private List<OfficePerformance> GetMostActiveOffices(List<Offer> offers)
        {
            return offers
                .GroupBy(o => o.ScientificOffice)
                .Select(g => new OfficePerformance
                {
                    OfficeName = g.Key,
                    OfferCount = g.Count(),
                    AveragePrice = g.Average(o => o.Price)
                })
                .OrderByDescending(p => p.OfferCount)
                .Take(10)
                .ToList();
        }
    }
}
