using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة التقارير المتقدمة
    /// </summary>
    public partial class AdvancedReportsWindow : Window
    {
        private readonly IOfferService _offerService;

        public string SelectedReportType { get; private set; } = string.Empty;
        public DateTime FromDate { get; private set; } = DateTime.Now.AddMonths(-3);
        public DateTime ToDate { get; private set; } = DateTime.Now;
        public string SelectedMaterial { get; private set; } = string.Empty;

        public AdvancedReportsWindow()
        {
            InitializeComponent();
            
            _offerService = App.ServiceProvider.GetRequiredService<IOfferService>();
            
            // تحديد التواريخ الافتراضية
            FromDatePicker.SelectedDate = FromDate;
            ToDatePicker.SelectedDate = ToDate;
            
            Loaded += AdvancedReportsWindow_Loaded;
        }

        private async void AdvancedReportsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل قائمة المواد العلمية
                var offers = await _offerService.GetAllOffersAsync();
                var materials = offers.Select(o => o.ScientificName).Distinct().OrderBy(m => m).ToList();
                
                MaterialComboBox.ItemsSource = materials;
                if (materials.Any())
                {
                    MaterialComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المواد العلمية: {ex.Message}");
            }
        }

        private void PriceTrendsRadio_Checked(object sender, RoutedEventArgs e)
        {
            if (DateRangeGrid != null)
            {
                DateRangeGrid.Visibility = Visibility.Visible;
            }
        }

        private void ComparisonRadio_Checked(object sender, RoutedEventArgs e)
        {
            if (MaterialComboBox != null)
            {
                MaterialComboBox.Visibility = Visibility.Visible;
            }
        }

        private void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديد نوع التقرير المحدد
                if (BestOffersRadio.IsChecked == true)
                {
                    SelectedReportType = "BestOffers";
                }
                else if (PriceTrendsRadio.IsChecked == true)
                {
                    SelectedReportType = "PriceTrends";
                    
                    // التحقق من صحة التواريخ
                    if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue)
                    {
                        FromDate = FromDatePicker.SelectedDate.Value;
                        ToDate = ToDatePicker.SelectedDate.Value;
                        
                        if (FromDate > ToDate)
                        {
                            MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ في التاريخ", 
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }
                    }
                    else
                    {
                        MessageBox.Show("يرجى تحديد نطاق التاريخ", "تاريخ مطلوب", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
                else if (ComparisonRadio.IsChecked == true)
                {
                    SelectedReportType = "Comparison";
                    
                    if (MaterialComboBox.SelectedItem != null)
                    {
                        SelectedMaterial = MaterialComboBox.SelectedItem.ToString() ?? string.Empty;
                    }
                    else
                    {
                        MessageBox.Show("يرجى اختيار المادة العلمية", "مادة علمية مطلوبة", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
