using HR_InvoiceArchiver.Utils;
using System.IO;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة مرفقات العروض
    /// </summary>
    public class OfferAttachmentService : IOfferAttachmentService
    {
        private readonly ICloudStorageService? _cloudStorageService;
        private readonly ILoggingService? _loggingService;

        // أنواع الملفات المدعومة
        private readonly string[] _supportedExtensions = {
            ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif",
            ".doc", ".docx", ".txt", ".rtf", ".xls", ".xlsx"
        };

        // الحد الأقصى لحجم الملف (10 ميجابايت)
        private const long MaxFileSize = 10 * 1024 * 1024;

        public OfferAttachmentService(ICloudStorageService? cloudStorageService = null, ILoggingService? loggingService = null)
        {
            _cloudStorageService = cloudStorageService;
            _loggingService = loggingService;
        }

        public async Task<string?> UploadAttachmentAsync(string sourceFilePath, int offerId)
        {
            return await UploadAttachmentAsync(sourceFilePath, offerId, false);
        }

        public async Task<string?> UploadAttachmentAsync(string sourceFilePath, int offerId, bool uploadToCloud)
        {
            try
            {
                // التحقق من صحة الملف
                var validation = ValidateFile(sourceFilePath);
                if (!validation.IsValid)
                {
                    var errors = string.Join(", ", validation.Errors);
                    throw new InvalidOperationException($"الملف غير صحيح: {errors}");
                }

                // حفظ الملف محلياً
                var relativePath = await FileHelper.SaveOfferAttachmentAsync(sourceFilePath, offerId);
                if (string.IsNullOrEmpty(relativePath))
                {
                    throw new InvalidOperationException("فشل في حفظ الملف محلياً");
                }

                // رفع إلى التخزين السحابي إذا كان مطلوباً
                if (uploadToCloud && _cloudStorageService != null)
                {
                    try
                    {
                        var fullPath = FileHelper.GetFullAttachmentPath(relativePath, "Offers");
                        var cloudFileName = $"Offers/{Path.GetFileName(relativePath)}";
                        await _cloudStorageService.UploadFileAsync(fullPath, cloudFileName, "Offers");
                        
                        _loggingService?.LogInfo($"تم رفع مرفق العرض {offerId} إلى التخزين السحابي");
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogWarning($"فشل في رفع مرفق العرض {offerId} إلى التخزين السحابي: {ex.Message}");
                        // لا نرمي استثناء هنا لأن الحفظ المحلي نجح
                    }
                }

                _loggingService?.LogInfo($"تم رفع مرفق للعرض {offerId}: {relativePath}");
                return relativePath;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"خطأ في رفع مرفق العرض {offerId}: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteAttachmentAsync(string? attachmentPath, bool deleteFromCloud = false)
        {
            try
            {
                if (string.IsNullOrEmpty(attachmentPath))
                    return false;

                var fullPath = FileHelper.GetFullAttachmentPath(attachmentPath, "Offers");
                
                // حذف من التخزين السحابي أولاً
                if (deleteFromCloud && _cloudStorageService != null)
                {
                    try
                    {
                        var cloudFileName = $"Offers/{Path.GetFileName(attachmentPath)}";
                        await _cloudStorageService.DeleteFileAsync(cloudFileName);
                        _loggingService?.LogInfo($"تم حذف المرفق من التخزين السحابي: {attachmentPath}");
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogWarning($"فشل في حذف المرفق من التخزين السحابي: {ex.Message}");
                    }
                }

                // حذف الملف المحلي
                var deleted = FileHelper.DeleteAttachment(fullPath);
                if (deleted)
                {
                    _loggingService?.LogInfo($"تم حذف مرفق العرض: {attachmentPath}");
                }

                return deleted;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"خطأ في حذف مرفق العرض: {ex.Message}");
                return false;
            }
        }

        public void OpenAttachment(string? attachmentPath)
        {
            try
            {
                if (string.IsNullOrEmpty(attachmentPath))
                    throw new ArgumentException("مسار المرفق فارغ");

                FileHelper.OpenAttachment(attachmentPath, "Offers");
                _loggingService?.LogInfo($"تم فتح مرفق العرض: {attachmentPath}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"خطأ في فتح مرفق العرض: {ex.Message}");
                throw;
            }
        }

        public bool AttachmentExists(string? attachmentPath)
        {
            return FileHelper.AttachmentExists(attachmentPath, "Offers");
        }

        public AttachmentInfo? GetAttachmentInfo(string? attachmentPath)
        {
            try
            {
                if (string.IsNullOrEmpty(attachmentPath))
                    return null;

                var fullPath = FileHelper.GetFullAttachmentPath(attachmentPath, "Offers");
                if (!File.Exists(fullPath))
                    return null;

                var fileInfo = new FileInfo(fullPath);
                return new AttachmentInfo
                {
                    FileName = fileInfo.Name,
                    Extension = fileInfo.Extension,
                    SizeInBytes = fileInfo.Length,
                    SizeFormatted = FormatFileSize(fileInfo.Length),
                    CreatedDate = fileInfo.CreationTime,
                    ModifiedDate = fileInfo.LastWriteTime,
                    Exists = true,
                    FullPath = fullPath
                };
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"خطأ في الحصول على معلومات المرفق: {ex.Message}");
                return null;
            }
        }

        public FileValidationResult ValidateFile(string filePath)
        {
            var result = new FileValidationResult();

            try
            {
                // التحقق من وجود الملف
                if (!File.Exists(filePath))
                {
                    result.Errors.Add("الملف غير موجود");
                    return result;
                }

                var fileInfo = new FileInfo(filePath);
                result.FileSizeBytes = fileInfo.Length;
                result.FileExtension = fileInfo.Extension.ToLower();

                // التحقق من حجم الملف
                if (fileInfo.Length > MaxFileSize)
                {
                    result.Errors.Add($"حجم الملف كبير جداً. الحد الأقصى المسموح {MaxFileSize / (1024 * 1024)} ميجابايت");
                }

                // التحقق من نوع الملف
                if (!_supportedExtensions.Contains(result.FileExtension))
                {
                    result.Errors.Add($"نوع الملف غير مدعوم: {result.FileExtension}");
                }

                // تحديد نوع MIME
                result.MimeType = GetMimeType(result.FileExtension);

                // تحذيرات
                if (fileInfo.Length > 5 * 1024 * 1024) // أكبر من 5 ميجابايت
                {
                    result.Warnings.Add("حجم الملف كبير، قد يستغرق وقتاً أطول في الرفع");
                }

                result.IsValid = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من الملف: {ex.Message}");
            }

            return result;
        }

        public string[] GetSupportedFileTypes()
        {
            return _supportedExtensions.ToArray();
        }

        public string GetFileFilter()
        {
            var descriptions = new Dictionary<string, string>
            {
                { ".pdf", "ملفات PDF" },
                { ".jpg,.jpeg", "صور JPEG" },
                { ".png", "صور PNG" },
                { ".gif", "صور GIF" },
                { ".bmp", "صور BMP" },
                { ".tiff,.tif", "صور TIFF" },
                { ".doc,.docx", "مستندات Word" },
                { ".txt", "ملفات نصية" },
                { ".rtf", "ملفات RTF" },
                { ".xls,.xlsx", "ملفات Excel" }
            };

            var filters = new List<string>();
            
            foreach (var desc in descriptions)
            {
                var extensions = desc.Key.Split(',');
                var pattern = string.Join(";", extensions.Select(ext => $"*{ext}"));
                filters.Add($"{desc.Value}|{pattern}");
            }

            filters.Add("جميع الملفات المدعومة|" + string.Join(";", _supportedExtensions.Select(ext => $"*{ext}")));
            filters.Add("جميع الملفات|*.*");

            return string.Join("|", filters);
        }

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private static string GetMimeType(string extension)
        {
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                ".rtf" => "application/rtf",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                _ => "application/octet-stream"
            };
        }
    }
}
