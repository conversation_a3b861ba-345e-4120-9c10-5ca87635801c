using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة التحليل المتقدم للعروض
    /// </summary>
    public interface IAdvancedOfferAnalysisService
    {
        /// <summary>
        /// الحصول على أفضل العروض لكل مادة علمية
        /// </summary>
        /// <param name="offers">قائمة العروض</param>
        /// <returns>قائمة أفضل العروض مع التحليل</returns>
        Task<List<BestOfferResult>> GetBestOffers(List<Offer> offers);

        /// <summary>
        /// تحليل عرض واحد
        /// </summary>
        /// <param name="offer">العرض المراد تحليله</param>
        /// <returns>نتيجة التحليل</returns>
        Task<OfferAnalysisResult> AnalyzeOffer(Offer offer);

        /// <summary>
        /// مقارنة العروض لمادة علمية واحدة
        /// </summary>
        /// <param name="offers">قائمة العروض</param>
        /// <param name="scientificName">اسم المادة العلمية</param>
        /// <returns>قائمة مقارنة العروض</returns>
        Task<List<OfferComparison>> CompareOffers(List<Offer> offers, string scientificName);
    }

    /// <summary>
    /// نتيجة أفضل عرض لمادة علمية
    /// </summary>
    public class BestOfferResult
    {
        public string ScientificName { get; set; } = string.Empty;
        public Offer BestOffer { get; set; } = new();
        public decimal EffectivePrice { get; set; }
        public decimal BonusValue { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal TotalSavings { get; set; }
        public int CompetingOffersCount { get; set; }
        public PriceRange PriceRange { get; set; } = new();
        public string AnalysisDetails { get; set; } = string.Empty;
    }

    /// <summary>
    /// نطاق الأسعار
    /// </summary>
    public class PriceRange
    {
        public decimal MinPrice { get; set; }
        public decimal MaxPrice { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal PriceSpread => MaxPrice - MinPrice;
    }

    /// <summary>
    /// نتيجة تحليل العرض
    /// </summary>
    public class OfferAnalysisResult
    {
        public Offer Offer { get; set; } = new();
        public decimal OriginalPrice { get; set; }
        public decimal EffectivePrice { get; set; }
        public decimal BonusValue { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal QualityScore { get; set; }
        public string AnalysisDetails { get; set; } = string.Empty;
    }

    /// <summary>
    /// مقارنة العروض
    /// </summary>
    public class OfferComparison
    {
        public Offer Offer { get; set; } = new();
        public OfferAnalysisResult Analysis { get; set; } = new();
        public int Rank { get; set; }
        public List<string> CompetitiveAdvantages { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// تحليل البونص والخصم
    /// </summary>
    public class BonusDiscountAnalysis
    {
        public decimal BonusValue { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal BonusPercentage { get; set; }
        public decimal DiscountPercentage { get; set; }
        public int BonusQuantity { get; set; }
        public bool HasFreeShipping { get; set; }
        public bool HasFreeGifts { get; set; }
        public string Details { get; set; } = string.Empty;
    }

    /// <summary>
    /// واجهة خدمة تقارير العروض
    /// </summary>
    public interface IOfferReportService
    {
        Task<byte[]> GenerateComparisonReportAsync(string scientificName);
        Task<byte[]> GeneratePriceTrendReportAsync(DateTime fromDate, DateTime toDate);
        Task<byte[]> GenerateBestOffersReportAsync();
        Task<OfferTrendAnalysis> AnalyzePriceTrendsAsync(DateTime fromDate, DateTime toDate);
    }

    /// <summary>
    /// تحليل اتجاهات العروض
    /// </summary>
    public class OfferTrendAnalysis
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalOffers { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal MedianPrice { get; set; }
        public decimal PriceVolatility { get; set; }
        public TrendDirection OverallTrend { get; set; }
        public List<MaterialTrend> MaterialTrends { get; set; } = new();
        public List<OfficePerformance> TopPerformingOffices { get; set; } = new();
        public List<OfficePerformance> MostActiveOffices { get; set; } = new();
    }

    /// <summary>
    /// اتجاه المادة العلمية
    /// </summary>
    public class MaterialTrend
    {
        public string MaterialName { get; set; } = string.Empty;
        public int OfferCount { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal MinPrice { get; set; }
        public decimal MaxPrice { get; set; }
        public decimal PriceVolatility { get; set; }
        public TrendDirection Trend { get; set; }
    }

    /// <summary>
    /// أداء المكتب العلمي
    /// </summary>
    public class OfficePerformance
    {
        public string OfficeName { get; set; } = string.Empty;
        public int OfferCount { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal BestPriceRatio { get; set; }
    }

    /// <summary>
    /// اتجاه السعر
    /// </summary>
    public enum TrendDirection
    {
        Increasing,
        Decreasing,
        Stable
    }
}
