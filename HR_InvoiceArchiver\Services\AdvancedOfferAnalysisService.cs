using HR_InvoiceArchiver.Models;
using System.Text.RegularExpressions;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة التحليل المتقدم للعروض
    /// </summary>
    public class AdvancedOfferAnalysisService : IAdvancedOfferAnalysisService
    {
        private readonly ILoggingService? _loggingService;

        public AdvancedOfferAnalysisService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        public async Task<List<BestOfferResult>> GetBestOffers(List<Offer> offers)
        {
            try
            {
                var results = new List<BestOfferResult>();
                
                // تجميع العروض حسب المادة العلمية
                var groupedOffers = offers.GroupBy(o => o.ScientificName);

                foreach (var group in groupedOffers)
                {
                    var scientificName = group.Key;
                    var offersForMaterial = group.ToList();

                    // حساب أفضل عرض لهذه المادة
                    var bestOffer = await CalculateBestOffer(offersForMaterial);
                    
                    if (bestOffer != null)
                    {
                        results.Add(new BestOfferResult
                        {
                            ScientificName = scientificName,
                            BestOffer = bestOffer.Offer,
                            EffectivePrice = bestOffer.EffectivePrice,
                            BonusValue = bestOffer.BonusValue,
                            DiscountValue = bestOffer.DiscountValue,
                            TotalSavings = bestOffer.TotalSavings,
                            CompetingOffersCount = offersForMaterial.Count,
                            PriceRange = new PriceRange
                            {
                                MinPrice = offersForMaterial.Min(o => o.Price),
                                MaxPrice = offersForMaterial.Max(o => o.Price),
                                AveragePrice = offersForMaterial.Average(o => o.Price)
                            },
                            AnalysisDetails = bestOffer.AnalysisDetails
                        });
                    }
                }

                return results.OrderBy(r => r.EffectivePrice).ToList();
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في حساب أفضل العروض: {ex.Message}", ex);
                return new List<BestOfferResult>();
            }
        }

        public async Task<OfferAnalysisResult> AnalyzeOffer(Offer offer)
        {
            try
            {
                var result = new OfferAnalysisResult
                {
                    Offer = offer,
                    OriginalPrice = offer.Price
                };

                // تحليل البونص والخصم
                var bonusAnalysis = AnalyzeBonusOrDiscount(offer.BonusOrDiscount);
                result.BonusValue = bonusAnalysis.BonusValue;
                result.DiscountValue = bonusAnalysis.DiscountValue;
                result.AnalysisDetails = bonusAnalysis.Details;

                // حساب السعر الفعلي
                result.EffectivePrice = CalculateEffectivePrice(offer.Price, bonusAnalysis);
                result.TotalSavings = offer.Price - result.EffectivePrice;

                // تقييم جودة العرض
                result.QualityScore = CalculateQualityScore(offer, bonusAnalysis);

                return result;
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في تحليل العرض: {ex.Message}", ex);
                return new OfferAnalysisResult { Offer = offer, OriginalPrice = offer.Price, EffectivePrice = offer.Price };
            }
        }

        public async Task<List<OfferComparison>> CompareOffers(List<Offer> offers, string scientificName)
        {
            try
            {
                var relevantOffers = offers.Where(o => o.ScientificName == scientificName).ToList();
                var comparisons = new List<OfferComparison>();

                foreach (var offer in relevantOffers)
                {
                    var analysis = await AnalyzeOffer(offer);
                    var advantages = await CalculateCompetitiveAdvantages(offer, relevantOffers);

                    comparisons.Add(new OfferComparison
                    {
                        Offer = offer,
                        Analysis = analysis,
                        Rank = 0, // سيتم تحديده لاحقاً
                        CompetitiveAdvantages = advantages,
                        Recommendations = GenerateRecommendations(analysis, relevantOffers)
                    });
                }

                // ترتيب العروض وتحديد الرتب
                comparisons = comparisons.OrderBy(c => c.Analysis.EffectivePrice).ToList();
                for (int i = 0; i < comparisons.Count; i++)
                {
                    comparisons[i].Rank = i + 1;
                }

                return comparisons;
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                    await _loggingService.LogErrorAsync($"خطأ في مقارنة العروض: {ex.Message}", ex);
                return new List<OfferComparison>();
            }
        }

        private async Task<OfferAnalysisResult?> CalculateBestOffer(List<Offer> offers)
        {
            if (!offers.Any()) return null;

            var analyzedOffers = new List<OfferAnalysisResult>();
            foreach (var offer in offers)
            {
                var analysis = await AnalyzeOffer(offer);
                analyzedOffers.Add(analysis);
            }

            // ترتيب حسب السعر الفعلي ثم نقاط الجودة
            var bestOffer = analyzedOffers
                .OrderBy(a => a.EffectivePrice)
                .ThenByDescending(a => a.QualityScore)
                .FirstOrDefault();

            return bestOffer;
        }

        private BonusDiscountAnalysis AnalyzeBonusOrDiscount(string? bonusOrDiscount)
        {
            var analysis = new BonusDiscountAnalysis();
            
            if (string.IsNullOrWhiteSpace(bonusOrDiscount))
            {
                analysis.Details = "لا يوجد بونص أو خصم";
                return analysis;
            }

            var text = bonusOrDiscount.Trim();
            var details = new List<string>();

            // البحث عن نسب مئوية
            var percentageMatches = Regex.Matches(text, @"(\d+(?:\.\d+)?)\s*%");
            foreach (Match match in percentageMatches)
            {
                if (decimal.TryParse(match.Groups[1].Value, out decimal percentage))
                {
                    if (text.Contains("خصم") || text.Contains("تخفيض"))
                    {
                        analysis.DiscountPercentage = Math.Max(analysis.DiscountPercentage, percentage);
                        details.Add($"خصم {percentage}%");
                    }
                    else if (text.Contains("بونص") || text.Contains("إضافي"))
                    {
                        analysis.BonusPercentage = Math.Max(analysis.BonusPercentage, percentage);
                        details.Add($"بونص {percentage}%");
                    }
                }
            }

            // البحث عن مبالغ ثابتة
            var amountMatches = Regex.Matches(text, @"(\d+(?:\.\d+)?)\s*(?:ريال|جنيه|دولار|درهم)");
            foreach (Match match in amountMatches)
            {
                if (decimal.TryParse(match.Groups[1].Value, out decimal amount))
                {
                    if (text.Contains("خصم") || text.Contains("تخفيض"))
                    {
                        analysis.DiscountValue = Math.Max(analysis.DiscountValue, amount);
                        details.Add($"خصم {amount} وحدة نقدية");
                    }
                    else if (text.Contains("بونص") || text.Contains("إضافي"))
                    {
                        analysis.BonusValue = Math.Max(analysis.BonusValue, amount);
                        details.Add($"بونص {amount} وحدة نقدية");
                    }
                }
            }

            // البحث عن كميات إضافية
            var quantityMatches = Regex.Matches(text, @"(\d+)\s*(?:قطعة|عبوة|كيس|علبة)\s*(?:مجان|إضافي|بونص)");
            foreach (Match match in quantityMatches)
            {
                if (int.TryParse(match.Groups[1].Value, out int quantity))
                {
                    analysis.BonusQuantity = Math.Max(analysis.BonusQuantity, quantity);
                    details.Add($"بونص {quantity} قطعة");
                }
            }

            // تحليل نصوص خاصة
            if (text.Contains("مجان") || text.Contains("هدية"))
            {
                details.Add("يتضمن هدايا مجانية");
                analysis.HasFreeGifts = true;
            }

            if (text.Contains("شحن مجان") || text.Contains("توصيل مجان"))
            {
                details.Add("شحن مجاني");
                analysis.HasFreeShipping = true;
            }

            analysis.Details = details.Any() ? string.Join(", ", details) : "تحليل نصي للبونص/الخصم";
            return analysis;
        }

        private decimal CalculateEffectivePrice(decimal originalPrice, BonusDiscountAnalysis analysis)
        {
            var effectivePrice = originalPrice;

            // تطبيق الخصم المئوي
            if (analysis.DiscountPercentage > 0)
            {
                effectivePrice -= (effectivePrice * analysis.DiscountPercentage / 100);
            }

            // تطبيق الخصم الثابت
            if (analysis.DiscountValue > 0)
            {
                effectivePrice -= analysis.DiscountValue;
            }

            // تقدير قيمة البونص (تحويل إلى خصم مكافئ)
            if (analysis.BonusPercentage > 0)
            {
                // البونص يعادل خصم بنسبة أقل
                var equivalentDiscount = analysis.BonusPercentage * 0.7m; // 70% من قيمة البونص
                effectivePrice -= (originalPrice * equivalentDiscount / 100);
            }

            if (analysis.BonusValue > 0)
            {
                effectivePrice -= (analysis.BonusValue * 0.7m); // 70% من قيمة البونص
            }

            // تقدير قيمة الكمية الإضافية
            if (analysis.BonusQuantity > 0)
            {
                var estimatedValuePerUnit = originalPrice * 0.1m; // تقدير 10% من السعر لكل وحدة إضافية
                effectivePrice -= (analysis.BonusQuantity * estimatedValuePerUnit);
            }

            // تقدير قيمة المزايا الإضافية
            if (analysis.HasFreeShipping)
            {
                effectivePrice -= (originalPrice * 0.02m); // خصم 2% للشحن المجاني
            }

            if (analysis.HasFreeGifts)
            {
                effectivePrice -= (originalPrice * 0.03m); // خصم 3% للهدايا المجانية
            }

            return Math.Max(0, effectivePrice); // لا يمكن أن يكون السعر سالباً
        }

        private decimal CalculateQualityScore(Offer offer, BonusDiscountAnalysis analysis)
        {
            decimal score = 50; // نقطة البداية

            // نقاط للبونص والخصم
            score += analysis.DiscountPercentage * 2;
            score += analysis.BonusPercentage * 1.5m;
            score += (analysis.DiscountValue / offer.Price) * 100 * 2;
            score += (analysis.BonusValue / offer.Price) * 100 * 1.5m;
            score += analysis.BonusQuantity * 5;

            // نقاط للمزايا الإضافية
            if (analysis.HasFreeShipping) score += 5;
            if (analysis.HasFreeGifts) score += 3;

            // نقاط لوجود مرفقات
            if (!string.IsNullOrEmpty(offer.AttachmentPath)) score += 10;

            // نقاط لوجود ملاحظات مفصلة
            if (!string.IsNullOrEmpty(offer.Notes) && offer.Notes.Length > 20) score += 5;

            return Math.Min(100, Math.Max(0, score)); // النتيجة بين 0 و 100
        }

        private async Task<List<string>> CalculateCompetitiveAdvantages(Offer offer, List<Offer> competingOffers)
        {
            var advantages = new List<string>();
            var analysis = await AnalyzeOffer(offer);
            var bonusAnalysis = AnalyzeBonusOrDiscount(offer.BonusOrDiscount);

            // مقارنة السعر
            var cheaperCount = 0;
            foreach (var competingOffer in competingOffers)
            {
                var competingAnalysis = await AnalyzeOffer(competingOffer);
                if (competingAnalysis.EffectivePrice > analysis.EffectivePrice)
                    cheaperCount++;
            }

            if (cheaperCount > 0)
            {
                advantages.Add($"أرخص من {cheaperCount} عرض آخر");
            }

            // مقارنة البونص
            if (analysis.BonusValue > 0 || bonusAnalysis.BonusPercentage > 0)
            {
                var withBonusCount = competingOffers.Count(o => !string.IsNullOrEmpty(o.BonusOrDiscount));
                if (withBonusCount < competingOffers.Count)
                {
                    advantages.Add("يتضمن بونص إضافي");
                }
            }

            // وجود مرفقات
            if (!string.IsNullOrEmpty(offer.AttachmentPath))
            {
                var withAttachmentsCount = competingOffers.Count(o => !string.IsNullOrEmpty(o.AttachmentPath));
                if (withAttachmentsCount < competingOffers.Count)
                {
                    advantages.Add("يتضمن مرفقات توضيحية");
                }
            }

            return advantages;
        }

        private List<string> GenerateRecommendations(OfferAnalysisResult analysis, List<Offer> allOffers)
        {
            var recommendations = new List<string>();

            // توصيات بناءً على السعر
            var avgPrice = allOffers.Average(o => o.Price);
            if (analysis.EffectivePrice < avgPrice * 0.9m)
            {
                recommendations.Add("عرض ممتاز - السعر أقل من المتوسط بشكل كبير");
            }
            else if (analysis.EffectivePrice > avgPrice * 1.1m)
            {
                recommendations.Add("قد تحتاج لمراجعة السعر - أعلى من المتوسط");
            }

            // توصيات بناءً على الجودة
            if (analysis.QualityScore >= 80)
            {
                recommendations.Add("عرض عالي الجودة - يُنصح بالاختيار");
            }
            else if (analysis.QualityScore < 40)
            {
                recommendations.Add("عرض منخفض الجودة - يحتاج تحسين");
            }

            // توصيات بناءً على التوفير
            if (analysis.TotalSavings > analysis.OriginalPrice * 0.15m)
            {
                recommendations.Add("توفير ممتاز - أكثر من 15% من السعر الأصلي");
            }

            return recommendations;
        }
    }
}
