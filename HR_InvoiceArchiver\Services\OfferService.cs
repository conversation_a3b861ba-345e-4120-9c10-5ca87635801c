using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Data;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة العروض - نسخة مبسطة
    /// </summary>
    public class OfferService : IOfferService
    {
        private readonly IOfferRepository _offerRepository;
        private readonly DatabaseContext _context;

        public OfferService(IOfferRepository offerRepository, DatabaseContext context)
        {
            _offerRepository = offerRepository;
            _context = context;
        }

        // العمليات الأساسية
        public async Task<IEnumerable<Offer>> GetAllOffersAsync()
        {
            return await _offerRepository.GetAllAsync();
        }

        public async Task<IEnumerable<Offer>> GetActiveOffersAsync()
        {
            return await _offerRepository.GetActiveOffersAsync();
        }

        public async Task<Offer?> GetOfferByIdAsync(int id)
        {
            return await _offerRepository.GetByIdAsync(id);
        }

        public async Task<Offer> CreateOfferAsync(Offer offer)
        {
            // التحقق من صحة البيانات
            if (!await ValidateOfferAsync(offer))
                throw new ValidationException("بيانات العرض غير صحيحة");

            // إنشاء أو الحصول على اسم المادة العلمية
            await GetOrCreateScientificNameAsync(offer.ScientificName);

            return await _offerRepository.CreateAsync(offer);
        }

        public async Task<Offer> UpdateOfferAsync(Offer offer)
        {
            // التحقق من صحة البيانات
            if (!await ValidateOfferAsync(offer))
                throw new ValidationException("بيانات العرض غير صحيحة");

            // التحقق من وجود العرض
            var existingOffer = await _offerRepository.GetByIdAsync(offer.Id);
            if (existingOffer == null)
                throw new ArgumentException("العرض غير موجود");

            // إنشاء أو الحصول على اسم المادة العلمية
            await GetOrCreateScientificNameAsync(offer.ScientificName);

            return await _offerRepository.UpdateAsync(offer);
        }

        public async Task<bool> DeleteOfferAsync(int id)
        {
            return await _offerRepository.SoftDeleteAsync(id);
        }

        public Task<bool> ValidateOfferAsync(Offer offer)
        {
            var validationContext = new ValidationContext(offer);
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            
            bool isValid = Validator.TryValidateObject(offer, validationContext, validationResults, true);
            
            // تحقق إضافي من منطق الأعمال
            if (string.IsNullOrWhiteSpace(offer.ScientificOffice))
                return Task.FromResult(false);
            
            if (string.IsNullOrWhiteSpace(offer.RepresentativeName))
                return Task.FromResult(false);
            
            if (string.IsNullOrWhiteSpace(offer.ScientificName))
                return Task.FromResult(false);
            
            if (offer.Price <= 0)
                return Task.FromResult(false);

            return Task.FromResult(isValid);
        }

        // البحث الأساسي
        public async Task<IEnumerable<Offer>> SearchOffersAsync(string searchTerm)
        {
            return await _offerRepository.SearchAsync(searchTerm);
        }

        public async Task<IEnumerable<Offer>> GetOffersByScientificNameAsync(string scientificName)
        {
            return await _offerRepository.GetOffersByScientificNameAsync(scientificName);
        }

        // إحصائيات أساسية
        public async Task<OfferStatistics> GetOfferStatisticsAsync()
        {
            try
            {
                var allOffers = await _offerRepository.GetAllAsync();
                var activeOffers = allOffers.Where(o => o.IsActive).ToList();

                return new OfferStatistics
                {
                    TotalOffers = allOffers.Count(),
                    ActiveOffers = activeOffers.Count,
                    UniqueScientificNames = activeOffers.Select(o => o.ScientificName).Distinct().Count(),
                    UniqueOffices = activeOffers.Select(o => o.ScientificOffice).Distinct().Count(),
                    AveragePrice = activeOffers.Any() ? activeOffers.Average(o => o.Price) : 0,
                    LastOfferDate = activeOffers.Any() ? activeOffers.Max(o => o.CreatedAt) : null
                };
            }
            catch (Exception)
            {
                return new OfferStatistics();
            }
        }

        // أفضل العروض
        public async Task<IEnumerable<Offer>> GetBestOffersAsync()
        {
            try
            {
                var activeOffers = await _offerRepository.GetActiveOffersAsync();

                // استخدام التحليل المتقدم إذا كان متوفراً
                try
                {
                    var analysisService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions
                        .GetService<IAdvancedOfferAnalysisService>(App.ServiceProvider);

                    if (analysisService != null)
                    {
                        var bestOfferResults = await analysisService.GetBestOffers(activeOffers.ToList());
                        return bestOfferResults.Select(r => r.BestOffer).OrderBy(o => o.ScientificName);
                    }
                }
                catch
                {
                    // في حالة فشل التحليل المتقدم، استخدم الطريقة البسيطة
                }

                // الطريقة البسيطة كبديل
                var bestOffers = activeOffers
                    .GroupBy(o => o.ScientificName.ToLower())
                    .Select(group => group.OrderBy(o => o.Price).First())
                    .OrderBy(o => o.ScientificName)
                    .ToList();

                return bestOffers;
            }
            catch (Exception)
            {
                return new List<Offer>();
            }
        }

        // التحليل المتقدم
        public async Task<List<BestOfferResult>> GetBestOffersWithAnalysisAsync()
        {
            try
            {
                var activeOffers = await _offerRepository.GetActiveOffersAsync();
                var analysisService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions
                    .GetService<IAdvancedOfferAnalysisService>(App.ServiceProvider);

                if (analysisService != null)
                {
                    return await analysisService.GetBestOffers(activeOffers.ToList());
                }

                return new List<BestOfferResult>();
            }
            catch (Exception)
            {
                return new List<BestOfferResult>();
            }
        }

        public async Task<OfferAnalysisResult> AnalyzeOfferAsync(int offerId)
        {
            try
            {
                var offer = await _offerRepository.GetByIdAsync(offerId);
                if (offer == null)
                    return new OfferAnalysisResult();

                var analysisService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions
                    .GetService<IAdvancedOfferAnalysisService>(App.ServiceProvider);

                if (analysisService != null)
                {
                    return await analysisService.AnalyzeOffer(offer);
                }

                return new OfferAnalysisResult { Offer = offer, OriginalPrice = offer.Price, EffectivePrice = offer.Price };
            }
            catch (Exception)
            {
                return new OfferAnalysisResult();
            }
        }

        public async Task<List<OfferComparison>> CompareOffersForMaterialAsync(string scientificName)
        {
            try
            {
                var allOffers = await _offerRepository.GetActiveOffersAsync();
                var analysisService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions
                    .GetService<IAdvancedOfferAnalysisService>(App.ServiceProvider);

                if (analysisService != null)
                {
                    return await analysisService.CompareOffers(allOffers.ToList(), scientificName);
                }

                return new List<OfferComparison>();
            }
            catch (Exception)
            {
                return new List<OfferComparison>();
            }
        }

        // تصدير البيانات
        public async Task<byte[]> ExportOffersToExcelAsync(IEnumerable<Offer> offers, string title = "تقرير العروض")
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add(title);

                // إعداد الرؤوس
                var headers = new[]
                {
                    "المكتب العلمي", "اسم المندوب", "رقم المندوب", "المادة العلمية",
                    "المادة التجارية", "السعر", "البونص/الخصم", "المرفق",
                    "تاريخ الإضافة", "الملاحظات"
                };

                // كتابة الرؤوس
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                    worksheet.Cells[1, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }

                // كتابة البيانات
                var offersList = offers.ToList();
                for (int row = 0; row < offersList.Count; row++)
                {
                    var offer = offersList[row];
                    var excelRow = row + 2;

                    worksheet.Cells[excelRow, 1].Value = offer.ScientificOffice;
                    worksheet.Cells[excelRow, 2].Value = offer.RepresentativeName;
                    worksheet.Cells[excelRow, 3].Value = offer.RepresentativePhone ?? "";
                    worksheet.Cells[excelRow, 4].Value = offer.ScientificName;
                    worksheet.Cells[excelRow, 5].Value = offer.TradeName ?? "";
                    worksheet.Cells[excelRow, 6].Value = offer.Price;
                    worksheet.Cells[excelRow, 7].Value = offer.BonusOrDiscount ?? "";
                    worksheet.Cells[excelRow, 8].Value = offer.AttachmentInfo;
                    worksheet.Cells[excelRow, 9].Value = offer.CreatedAt.ToString("yyyy/MM/dd");
                    worksheet.Cells[excelRow, 10].Value = offer.Notes ?? "";

                    // تنسيق الحدود
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Cells[excelRow, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                }

                // تنسيق عمود السعر
                var priceColumn = worksheet.Cells[2, 6, offersList.Count + 1, 6];
                priceColumn.Style.Numberformat.Format = "#,##0.00";

                // ضبط عرض الأعمدة
                worksheet.Cells.AutoFitColumns();

                // إضافة معلومات إضافية
                var lastRow = offersList.Count + 3;
                worksheet.Cells[lastRow, 1].Value = "إجمالي العروض:";
                worksheet.Cells[lastRow, 2].Value = offersList.Count;
                worksheet.Cells[lastRow, 1].Style.Font.Bold = true;

                worksheet.Cells[lastRow + 1, 1].Value = "متوسط السعر:";
                worksheet.Cells[lastRow + 1, 2].Value = offersList.Any() ? offersList.Average(o => o.Price) : 0;
                worksheet.Cells[lastRow + 1, 2].Style.Numberformat.Format = "#,##0.00";
                worksheet.Cells[lastRow + 1, 1].Style.Font.Bold = true;

                worksheet.Cells[lastRow + 2, 1].Value = "تاريخ التقرير:";
                worksheet.Cells[lastRow + 2, 2].Value = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                worksheet.Cells[lastRow + 2, 1].Style.Font.Bold = true;

                return await Task.FromResult(package.GetAsByteArray());
            }
            catch (Exception)
            {
                throw;
            }
        }

        // خدمات المواد العلمية الأساسية
        public async Task<IEnumerable<ScientificName>> GetAllScientificNamesAsync()
        {
            try
            {
                return await _context.ScientificNames
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<ScientificName>();
            }
        }

        public async Task<ScientificName> GetOrCreateScientificNameAsync(string name, string? category = null, string? description = null)
        {
            try
            {
                var existing = await _context.ScientificNames
                    .FirstOrDefaultAsync(s => s.Name.ToLower() == name.ToLower());
                
                if (existing != null)
                    return existing;

                var newScientificName = new ScientificName
                {
                    Name = name.Trim(),
                    Category = category?.Trim(),
                    Description = description?.Trim(),
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                _context.ScientificNames.Add(newScientificName);
                await _context.SaveChangesAsync();
                return newScientificName;
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
